// ملف وهمي للتطوير - يحاكي Supabase Server Client
import { cookies } from 'next/headers'

// عميل وهمي للخادم
export function createClient(cookieStore: any) {
  return {
    auth: {
      getUser: () => Promise.resolve({ 
        data: { 
          user: { 
            id: 'mock-user-id', 
            email: '<EMAIL>' 
          } 
        }, 
        error: null 
      })
    },
    from: (table: string) => ({
      select: (columns?: string) => ({
        eq: (column: string, value: any) => ({
          single: () => Promise.resolve({ 
            data: { 
              id: 'mock-id', 
              role: 'admin' 
            }, 
            error: null 
          })
        }),
        order: (column: string, options?: any) => ({
          limit: (count: number) => ({
            range: (start: number, end: number) => Promise.resolve({
              data: [],
              error: null
            })
          })
        })
      }),
      insert: (data: any) => ({
        select: () => ({
          single: () => Promise.resolve({
            data: { id: 'new-mock-id', ...data },
            error: null
          })
        })
      }),
      update: (data: any) => ({
        eq: (column: string, value: any) => ({
          select: () => ({
            single: () => Promise.resolve({
              data: { id: value, ...data },
              error: null
            })
          })
        })
      }),
      delete: () => ({
        eq: (column: string, value: any) => Promise.resolve({
          error: null
        })
      })
    }),
    storage: {
      from: (bucket: string) => ({
        upload: (path: string, file: any, options?: any) => Promise.resolve({
          data: { id: 'mock-file-id', path },
          error: null
        }),
        getPublicUrl: (path: string) => ({
          data: { publicUrl: `https://mock-storage.com/${path}` }
        }),
        remove: (paths: string[]) => Promise.resolve({
          error: null
        })
      })
    }
  }
}
