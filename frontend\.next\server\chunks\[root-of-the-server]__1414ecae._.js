module.exports = {

"[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// ملف وهمي للتطوير - يحاكي Supabase Server Client
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
function createClient(cookieStore) {
    return {
        auth: {
            getUser: ()=>Promise.resolve({
                    data: {
                        user: {
                            id: 'mock-user-id',
                            email: '<EMAIL>'
                        }
                    },
                    error: null
                })
        },
        from: (table)=>({
                select: (columns)=>({
                        eq: (column, value)=>({
                                single: ()=>Promise.resolve({
                                        data: {
                                            id: 'mock-id',
                                            role: 'admin'
                                        },
                                        error: null
                                    })
                            }),
                        order: (column, options)=>({
                                limit: (count)=>({
                                        range: (start, end)=>Promise.resolve({
                                                data: [],
                                                error: null
                                            })
                                    })
                            })
                    }),
                insert: (data)=>({
                        select: ()=>({
                                single: ()=>Promise.resolve({
                                        data: {
                                            id: 'new-mock-id',
                                            ...data
                                        },
                                        error: null
                                    })
                            })
                    }),
                update: (data)=>({
                        eq: (column, value)=>({
                                select: ()=>({
                                        single: ()=>Promise.resolve({
                                                data: {
                                                    id: value,
                                                    ...data
                                                },
                                                error: null
                                            })
                                    })
                            })
                    }),
                delete: ()=>({
                        eq: (column, value)=>Promise.resolve({
                                error: null
                            })
                    })
            }),
        storage: {
            from: (bucket)=>({
                    upload: (path, file, options)=>Promise.resolve({
                            data: {
                                id: 'mock-file-id',
                                path
                            },
                            error: null
                        }),
                    getPublicUrl: (path)=>({
                            data: {
                                publicUrl: `https://mock-storage.com/${path}`
                            }
                        }),
                    remove: (paths)=>Promise.resolve({
                            error: null
                        })
                })
        }
    };
}
}}),
"[project]/src/app/api/products/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DELETE": (()=>DELETE),
    "GET": (()=>GET),
    "POST": (()=>POST),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])(cookieStore);
        const { searchParams } = new URL(request.url);
        const category = searchParams.get('category');
        const available = searchParams.get('available');
        const limit = searchParams.get('limit');
        const offset = searchParams.get('offset');
        let query = supabase.from('products').select('*').order('created_at', {
            ascending: false
        });
        // تطبيق الفلاتر
        if (category && category !== 'all') {
            query = query.eq('category', category);
        }
        if (available === 'true') {
            query = query.eq('is_available', true);
        } else if (available === 'false') {
            query = query.eq('is_available', false);
        }
        // تطبيق التصفح
        if (limit) {
            query = query.limit(parseInt(limit));
        }
        if (offset) {
            query = query.range(parseInt(offset), parseInt(offset) + (parseInt(limit || '10') - 1));
        }
        const { data: products, error } = await query;
        if (error) {
            console.error('Error fetching products:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'فشل في جلب المنتجات'
            }, {
                status: 500
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            products
        });
    } catch (error) {
        console.error('Unexpected error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ غير متوقع'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])(cookieStore);
        // التحقق من صلاحيات المستخدم
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح لك بالوصول'
            }, {
                status: 401
            });
        }
        // التحقق من دور المستخدم
        const { data: profile } = await supabase.from('profiles').select('role').eq('id', user.id).single();
        if (!profile || profile.role !== 'admin') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح لك بإضافة المنتجات'
            }, {
                status: 403
            });
        }
        const body = await request.json();
        const { name, description, category, price, rental_price, colors, sizes, images, stock_quantity, is_available, features, specifications } = body;
        // التحقق من البيانات المطلوبة
        if (!name || !description || !category || !price || !colors || !sizes) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'البيانات المطلوبة مفقودة'
            }, {
                status: 400
            });
        }
        // إدراج المنتج في قاعدة البيانات
        const { data: product, error: insertError } = await supabase.from('products').insert({
            name,
            description,
            category,
            price: parseFloat(price),
            rental_price: rental_price ? parseFloat(rental_price) : null,
            colors,
            sizes,
            images: images || [],
            stock_quantity: parseInt(stock_quantity) || 0,
            is_available: is_available ?? true,
            features: features || [],
            specifications: specifications || {}
        }).select().single();
        if (insertError) {
            console.error('Error inserting product:', insertError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'فشل في إضافة المنتج'
            }, {
                status: 500
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'تم إضافة المنتج بنجاح',
            product
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Unexpected error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ غير متوقع'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])(cookieStore);
        // التحقق من صلاحيات المستخدم
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح لك بالوصول'
            }, {
                status: 401
            });
        }
        // التحقق من دور المستخدم
        const { data: profile } = await supabase.from('profiles').select('role').eq('id', user.id).single();
        if (!profile || profile.role !== 'admin') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح لك بتحديث المنتجات'
            }, {
                status: 403
            });
        }
        const body = await request.json();
        const { id, ...updateData } = body;
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'معرف المنتج مطلوب'
            }, {
                status: 400
            });
        }
        // تحديث المنتج
        const { data: product, error: updateError } = await supabase.from('products').update({
            ...updateData,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (updateError) {
            console.error('Error updating product:', updateError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'فشل في تحديث المنتج'
            }, {
                status: 500
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'تم تحديث المنتج بنجاح',
            product
        });
    } catch (error) {
        console.error('Unexpected error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ غير متوقع'
        }, {
            status: 500
        });
    }
}
async function DELETE(request) {
    try {
        const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])(cookieStore);
        // التحقق من صلاحيات المستخدم
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح لك بالوصول'
            }, {
                status: 401
            });
        }
        // التحقق من دور المستخدم
        const { data: profile } = await supabase.from('profiles').select('role').eq('id', user.id).single();
        if (!profile || profile.role !== 'admin') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'غير مصرح لك بحذف المنتجات'
            }, {
                status: 403
            });
        }
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'معرف المنتج مطلوب'
            }, {
                status: 400
            });
        }
        // حذف المنتج
        const { error: deleteError } = await supabase.from('products').delete().eq('id', id);
        if (deleteError) {
            console.error('Error deleting product:', deleteError);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'فشل في حذف المنتج'
            }, {
                status: 500
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'تم حذف المنتج بنجاح'
        });
    } catch (error) {
        console.error('Unexpected error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ غير متوقع'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1414ecae._.js.map